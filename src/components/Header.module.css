.header {
  background-color: var(--color-black);
  border-bottom: 2px solid var(--color-red);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: var(--spacing-sm) 0;
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.logo {
  text-decoration: none;
  color: var(--color-white);
}

.logo h1 {
  font-family: var(--font-script);
  font-size: 2.5rem;
  margin: 0;
  color: var(--color-white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.tagline {
  font-family: var(--font-sans);
  font-size: 0.875rem;
  color: var(--color-grey);
  display: block;
  margin-top: -0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.nav {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.navLink {
  color: var(--color-white);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
  position: relative;
}

.navLink:hover {
  color: var(--color-red);
  background-color: rgba(255, 255, 255, 0.1);
}

.navLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-red);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navLink:hover::after {
  width: 100%;
}

.ctaButtons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.mobileMenuButton {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  gap: 4px;
}

.hamburger {
  width: 25px;
  height: 3px;
  background-color: var(--color-white);
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobileNav {
  display: none;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--color-grey);
  margin-top: var(--spacing-sm);
}

.mobileNavOpen {
  display: flex;
}

.mobileNavLink {
  color: var(--color-white);
  text-decoration: none;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
  text-align: center;
  font-weight: 500;
}

.mobileNavLink:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--color-red);
}

.mobileCta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-grey);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav,
  .ctaButtons {
    display: none;
  }

  .mobileMenuButton {
    display: flex;
  }

  .logo h1 {
    font-size: 2rem;
  }

  .tagline {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .logo h1 {
    font-size: 1.75rem;
  }
  
  .headerContent {
    gap: var(--spacing-sm);
  }
}
