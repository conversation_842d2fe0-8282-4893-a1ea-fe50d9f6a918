/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Tuffside Brand Colors */
  --color-black: #000000;
  --color-white: #ffffff;
  --color-red: #dc2626;
  --color-grey: #6b7280;
  --color-blue: #1e40af;

  /* Typography */
  --font-script: var(--font-mr-dafoe), '<PERSON><PERSON><PERSON>', cursive;
  --font-sans: var(--font-inter), 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

body {
  background-color: var(--color-black);
  color: var(--color-white);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-script);
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: var(--spacing-sm);
}

h1 {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-sm);
}

a {
  color: var(--color-white);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--color-red);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.text-center {
  text-align: center;
}

.text-red {
  color: var(--color-red);
}

.text-grey {
  color: var(--color-grey);
}

.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-lg {
  margin-bottom: var(--spacing-lg);
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--color-white);
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--color-white);
  font-family: var(--font-sans);
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn:hover {
  background-color: var(--color-white);
  color: var(--color-black);
}

.btn-primary {
  background-color: var(--color-red);
  border-color: var(--color-red);
  color: var(--color-white);
}

.btn-primary:hover {
  background-color: var(--color-white);
  color: var(--color-red);
  border-color: var(--color-white);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.125rem;
}

/* Grid and Layout */
.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  flex-direction: column;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
  }

  .btn-large {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 1rem;
  }
}
