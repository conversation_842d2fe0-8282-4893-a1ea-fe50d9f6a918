{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/page.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"emergency\": \"page-module___8aEwW__emergency\",\n  \"emergencyActions\": \"page-module___8aEwW__emergencyActions\",\n  \"emergencyContent\": \"page-module___8aEwW__emergencyContent\",\n  \"hero\": \"page-module___8aEwW__hero\",\n  \"heroContent\": \"page-module___8aEwW__heroContent\",\n  \"heroDescription\": \"page-module___8aEwW__heroDescription\",\n  \"heroImage\": \"page-module___8aEwW__heroImage\",\n  \"heroSubtitle\": \"page-module___8aEwW__heroSubtitle\",\n  \"heroTagline\": \"page-module___8aEwW__heroTagline\",\n  \"heroText\": \"page-module___8aEwW__heroText\",\n  \"heroTitle\": \"page-module___8aEwW__heroTitle\",\n  \"homePage\": \"page-module___8aEwW__homePage\",\n  \"logoPlaceholder\": \"page-module___8aEwW__logoPlaceholder\",\n  \"serviceCard\": \"page-module___8aEwW__serviceCard\",\n  \"serviceIcon\": \"page-module___8aEwW__serviceIcon\",\n  \"servicesPreview\": \"page-module___8aEwW__servicesPreview\",\n  \"trustBadge\": \"page-module___8aEwW__trustBadge\",\n  \"trustNumber\": \"page-module___8aEwW__trustNumber\",\n  \"whyChooseUs\": \"page-module___8aEwW__whyChooseUs\",\n  \"whyContent\": \"page-module___8aEwW__whyContent\",\n  \"whyList\": \"page-module___8aEwW__whyList\",\n  \"whyText\": \"page-module___8aEwW__whyText\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/page.js"], "sourcesContent": ["import CTAButtons from '@/components/CTAButtons';\nimport styles from './page.module.css';\n\nexport default function Home() {\n  return (\n    <div className={styles.homePage}>\n      {/* Hero Section */}\n      <section className={styles.hero}>\n        <div className=\"container\">\n          <div className={styles.heroContent}>\n            <div className={styles.heroText}>\n              <h1 className={styles.heroTitle}>\n                Tuffside\n                <span className={styles.heroSubtitle}>Automotive Garage</span>\n              </h1>\n              <p className={styles.heroTagline}>Built to Last. Tuned to Perform.</p>\n              <p className={styles.heroDescription}>\n                Professional automotive repair and maintenance services in Trinidad.\n                Expert diagnostics, diesel repair, engine tuning, and suspension work\n                with honest, reliable service you can trust.\n              </p>\n              <CTAButtons layout=\"horizontal\" size=\"large\" />\n            </div>\n            <div className={styles.heroImage}>\n              <div className={styles.logoPlaceholder}>\n                <h2>TUFFSIDE</h2>\n                <p>AUTOMOTIVE</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Preview */}\n      <section className={styles.servicesPreview}>\n        <div className=\"container\">\n          <h2 className=\"text-center mb-lg\">Our Services</h2>\n          <div className=\"grid grid-3\">\n            <div className={styles.serviceCard}>\n              <div className={styles.serviceIcon}>🔧</div>\n              <h3>Engine Diagnostics</h3>\n              <p>Advanced diagnostic tools to identify and resolve engine issues quickly and accurately.</p>\n            </div>\n            <div className={styles.serviceCard}>\n              <div className={styles.serviceIcon}>⚙️</div>\n              <h3>Diesel Repair</h3>\n              <p>Specialized diesel engine repair and maintenance for trucks, buses, and heavy machinery.</p>\n            </div>\n            <div className={styles.serviceCard}>\n              <div className={styles.serviceIcon}>🏁</div>\n              <h3>Engine Tuning</h3>\n              <p>Performance tuning and optimization to get the best out of your vehicle's engine.</p>\n            </div>\n          </div>\n          <div className=\"text-center mt-lg\">\n            <a href=\"/services\" className=\"btn btn-primary btn-large\">\n              View All Services\n            </a>\n          </div>\n        </div>\n      </section>\n\n      {/* Why Choose Us */}\n      <section className={styles.whyChooseUs}>\n        <div className=\"container\">\n          <div className={styles.whyContent}>\n            <div className={styles.whyText}>\n              <h2>Why Choose Tuffside?</h2>\n              <ul className={styles.whyList}>\n                <li>✓ Over 15 years of automotive experience</li>\n                <li>✓ Honest, transparent pricing</li>\n                <li>✓ Quick turnaround times</li>\n                <li>✓ Emergency and on-site services</li>\n                <li>✓ Friendly, professional service</li>\n                <li>✓ All work guaranteed</li>\n              </ul>\n              <p>\n                We're not just another garage - we're your neighbors who care about\n                keeping you safely on the road. Every job, big or small, gets our\n                full attention and expertise.\n              </p>\n            </div>\n            <div className={styles.whyImage}>\n              <div className={styles.trustBadge}>\n                <h3>Trusted by</h3>\n                <p className={styles.trustNumber}>500+</p>\n                <p>Happy Customers</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Emergency Services */}\n      <section className={styles.emergency}>\n        <div className=\"container\">\n          <div className={styles.emergencyContent}>\n            <h2>Need Emergency Help?</h2>\n            <p>Stuck on the road? We offer emergency call-out services for breakdowns,\n            urgent repairs, and vehicle recovery across Trinidad.</p>\n            <div className={styles.emergencyActions}>\n              <a\n                href=\"tel:+18683357440\"\n                className=\"btn btn-primary btn-large\"\n              >\n                📞 Emergency Call\n              </a>\n              <a\n                href=\"https://wa.me/18683357440\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"btn btn-large\"\n              >\n                💬 WhatsApp Now\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,QAAQ;;0BAE7B,8OAAC;gBAAQ,WAAW,8HAAA,CAAA,UAAM,CAAC,IAAI;0BAC7B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,8OAAC;gCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,QAAQ;;kDAC7B,8OAAC;wCAAG,WAAW,8HAAA,CAAA,UAAM,CAAC,SAAS;;4CAAE;0DAE/B,8OAAC;gDAAK,WAAW,8HAAA,CAAA,UAAM,CAAC,YAAY;0DAAE;;;;;;;;;;;;kDAExC,8OAAC;wCAAE,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;kDAAE;;;;;;kDAClC,8OAAC;wCAAE,WAAW,8HAAA,CAAA,UAAM,CAAC,eAAe;kDAAE;;;;;;kDAKtC,8OAAC;wCAAW,QAAO;wCAAa,MAAK;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,SAAS;0CAC9B,cAAA,8OAAC;oCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,eAAe;;sDACpC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAQ,WAAW,8HAAA,CAAA,UAAM,CAAC,eAAe;0BACxC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoB;;;;;;sCAClC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;4CAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDACpC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAEL,8OAAC;oCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;4CAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDACpC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;8CAEL,8OAAC;oCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;4CAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDACpC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAGP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;0BAQhE,8OAAC;gBAAQ,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;0BACpC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,8OAAC;gCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,OAAO;;kDAC5B,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAG,WAAW,8HAAA,CAAA,UAAM,CAAC,OAAO;;0DAC3B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAEN,8OAAC;kDAAE;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,QAAQ;0CAC7B,cAAA,8OAAC;oCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,UAAU;;sDAC/B,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;4CAAE,WAAW,8HAAA,CAAA,UAAM,CAAC,WAAW;sDAAE;;;;;;sDAClC,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAQ,WAAW,8HAAA,CAAA,UAAM,CAAC,SAAS;0BAClC,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,gBAAgB;;0CACrC,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAE;;;;;;0CAEH,8OAAC;gCAAI,WAAW,8HAAA,CAAA,UAAM,CAAC,gBAAgB;;kDACrC,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}