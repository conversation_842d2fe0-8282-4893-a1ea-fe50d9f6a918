{"version": 3, "sources": [], "sections": [{"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Header.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"ctaButtons\": \"Header-module__hBw1pG__ctaButtons\",\n  \"hamburger\": \"Header-module__hBw1pG__hamburger\",\n  \"header\": \"Header-module__hBw1pG__header\",\n  \"headerContent\": \"Header-module__hBw1pG__headerContent\",\n  \"logo\": \"Header-module__hBw1pG__logo\",\n  \"mobileCta\": \"Header-module__hBw1pG__mobileCta\",\n  \"mobileMenuButton\": \"Header-module__hBw1pG__mobileMenuButton\",\n  \"mobileNav\": \"Header-module__hBw1pG__mobileNav\",\n  \"mobileNavLink\": \"Header-module__hBw1pG__mobileNavLink\",\n  \"mobileNavOpen\": \"Header-module__hBw1pG__mobileNavOpen\",\n  \"nav\": \"Header-module__hBw1pG__nav\",\n  \"navLink\": \"Header-module__hBw1pG__navLink\",\n  \"tagline\": \"Header-module__hBw1pG__tagline\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport styles from './Header.module.css';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  return (\n    <header className={styles.header}>\n      <div className=\"container\">\n        <div className={styles.headerContent}>\n          {/* Logo */}\n          <Link href=\"/\" className={styles.logo}>\n            <h1>Tuffside</h1>\n            <span className={styles.tagline}>Automotive Garage</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className={styles.nav}>\n            <Link href=\"/\" className={styles.navLink}>Home</Link>\n            <Link href=\"/about\" className={styles.navLink}>About</Link>\n            <Link href=\"/services\" className={styles.navLink}>Services</Link>\n            <Link href=\"/bookings\" className={styles.navLink}>Bookings</Link>\n            <Link href=\"/testimonials\" className={styles.navLink}>Testimonials</Link>\n            <Link href=\"/contact\" className={styles.navLink}>Contact</Link>\n          </nav>\n\n          {/* CTA Buttons */}\n          <div className={styles.ctaButtons}>\n            <a \n              href=\"tel:+18683357440\" \n              className=\"btn btn-primary\"\n              aria-label=\"Call Tuffside Automotive Garage\"\n            >\n              Call Now\n            </a>\n            <a \n              href=\"https://wa.me/18683357440\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              className=\"btn\"\n              aria-label=\"Message us on WhatsApp\"\n            >\n              WhatsApp\n            </a>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button \n            className={styles.mobileMenuButton}\n            onClick={toggleMenu}\n            aria-label=\"Toggle mobile menu\"\n            aria-expanded={isMenuOpen}\n          >\n            <span className={styles.hamburger}></span>\n            <span className={styles.hamburger}></span>\n            <span className={styles.hamburger}></span>\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <nav className={`${styles.mobileNav} ${isMenuOpen ? styles.mobileNavOpen : ''}`}>\n          <Link href=\"/\" className={styles.mobileNavLink} onClick={toggleMenu}>Home</Link>\n          <Link href=\"/about\" className={styles.mobileNavLink} onClick={toggleMenu}>About</Link>\n          <Link href=\"/services\" className={styles.mobileNavLink} onClick={toggleMenu}>Services</Link>\n          <Link href=\"/bookings\" className={styles.mobileNavLink} onClick={toggleMenu}>Bookings</Link>\n          <Link href=\"/testimonials\" className={styles.mobileNavLink} onClick={toggleMenu}>Testimonials</Link>\n          <Link href=\"/contact\" className={styles.mobileNavLink} onClick={toggleMenu}>Contact</Link>\n          \n          <div className={styles.mobileCta}>\n            <a \n              href=\"tel:+18683357440\" \n              className=\"btn btn-primary btn-large\"\n              onClick={toggleMenu}\n            >\n              Call Now\n            </a>\n            <a \n              href=\"https://wa.me/18683357440\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              className=\"btn btn-large\"\n              onClick={toggleMenu}\n            >\n              WhatsApp\n            </a>\n          </div>\n        </nav>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,8OAAC;QAAO,WAAW,uIAAA,CAAA,UAAM,CAAC,MAAM;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;;sCAElC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,IAAI;;8CACnC,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;oCAAK,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;;;;;;;sCAInC,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,GAAG;;8CACxB,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;8CAC1C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;8CAClD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;8CAClD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAgB,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;8CACtD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAW,uIAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;;;;;;;sCAInD,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,UAAU;;8CAC/B,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,cAAW;8CACZ;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,cAAW;8CACZ;;;;;;;;;;;;sCAMH,8OAAC;4BACC,WAAW,uIAAA,CAAA,UAAM,CAAC,gBAAgB;4BAClC,SAAS;4BACT,cAAW;4BACX,iBAAe;;8CAEf,8OAAC;oCAAK,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;8CACjC,8OAAC;oCAAK,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;8CACjC,8OAAC;oCAAK,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;;;;;;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAW,GAAG,uIAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,uIAAA,CAAA,UAAM,CAAC,aAAa,GAAG,IAAI;;sCAC7E,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;sCACrE,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;sCAC1E,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;sCAC7E,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;sCAC7E,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAgB,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;sCACjF,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAW,uIAAA,CAAA,UAAM,CAAC,aAAa;4BAAE,SAAS;sCAAY;;;;;;sCAE5E,8OAAC;4BAAI,WAAW,uIAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS;8CACV;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,SAAS;8CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}