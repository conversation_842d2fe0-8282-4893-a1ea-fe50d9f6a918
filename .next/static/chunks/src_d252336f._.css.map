{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/CTAButtons.module.css"], "sourcesContent": [".ctaContainer {\n  display: flex;\n  gap: var(--spacing-md);\n  align-items: center;\n  justify-content: center;\n}\n\n.horizontal {\n  flex-direction: row;\n  flex-wrap: wrap;\n}\n\n.vertical {\n  flex-direction: column;\n  align-items: stretch;\n}\n\n.default {\n  /* Default size - uses global btn styles */\n}\n\n.large {\n  /* Large size - uses btn-large class */\n}\n\n.small .btn {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  font-size: 0.875rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .horizontal {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .ctaContainer {\n    gap: var(--spacing-sm);\n  }\n}\n\n@media (max-width: 480px) {\n  .ctaContainer .btn {\n    width: 100%;\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;AAKA;;;;;AAaA;;;;;AAMA;EACE;;;;;EAKA;;;;;AAKF;EACE", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/page.module.css"], "sourcesContent": [".homePage {\n  min-height: 100vh;\n}\n\n/* Hero Section */\n.hero {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.hero::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"%23333\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n  opacity: 0.1;\n  z-index: 1;\n}\n\n.heroContent {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-2xl);\n  align-items: center;\n  position: relative;\n  z-index: 2;\n}\n\n.heroText {\n  max-width: 600px;\n}\n\n.heroTitle {\n  font-family: var(--font-script);\n  font-size: 4rem;\n  line-height: 1;\n  margin-bottom: var(--spacing-sm);\n  color: var(--color-white);\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.heroSubtitle {\n  display: block;\n  font-size: 1.5rem;\n  color: var(--color-red);\n  margin-top: var(--spacing-xs);\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.heroTagline {\n  font-size: 1.5rem;\n  color: var(--color-red);\n  font-weight: 600;\n  margin-bottom: var(--spacing-md);\n  font-style: italic;\n}\n\n.heroDescription {\n  font-size: 1.125rem;\n  line-height: 1.6;\n  color: var(--color-grey);\n  margin-bottom: var(--spacing-xl);\n}\n\n.heroImage {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.logoPlaceholder {\n  width: 300px;\n  height: 300px;\n  border: 3px solid var(--color-red);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  background: linear-gradient(45deg, var(--color-black), #2a2a2a);\n  box-shadow: 0 0 30px rgba(220, 38, 38, 0.3);\n}\n\n.logoPlaceholder h2 {\n  font-family: var(--font-script);\n  font-size: 2.5rem;\n  color: var(--color-white);\n  margin: 0;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.logoPlaceholder p {\n  font-size: 1rem;\n  color: var(--color-red);\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.2em;\n}\n\n/* Services Preview */\n.servicesPreview {\n  padding: var(--spacing-2xl) 0;\n  background-color: #111111;\n}\n\n.serviceCard {\n  background-color: var(--color-black);\n  border: 2px solid var(--color-grey);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n  text-align: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.serviceCard::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.1), transparent);\n  transition: left 0.5s ease;\n}\n\n.serviceCard:hover::before {\n  left: 100%;\n}\n\n.serviceCard:hover {\n  border-color: var(--color-red);\n  transform: translateY(-5px);\n  box-shadow: var(--shadow-lg);\n}\n\n.serviceIcon {\n  font-size: 3rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.serviceCard h3 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-sm);\n}\n\n.serviceCard p {\n  color: var(--color-grey);\n  line-height: 1.6;\n}\n\n/* Why Choose Us */\n.whyChooseUs {\n  padding: var(--spacing-2xl) 0;\n  background-color: var(--color-black);\n}\n\n.whyContent {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: var(--spacing-2xl);\n  align-items: center;\n}\n\n.whyText h2 {\n  color: var(--color-white);\n  margin-bottom: var(--spacing-lg);\n}\n\n.whyList {\n  list-style: none;\n  padding: 0;\n  margin-bottom: var(--spacing-lg);\n}\n\n.whyList li {\n  color: var(--color-grey);\n  padding: var(--spacing-xs) 0;\n  font-size: 1.125rem;\n  transition: color 0.3s ease;\n}\n\n.whyList li:hover {\n  color: var(--color-white);\n}\n\n.whyText p {\n  color: var(--color-grey);\n  font-size: 1.125rem;\n  line-height: 1.6;\n}\n\n.trustBadge {\n  background: linear-gradient(135deg, var(--color-red), #b91c1c);\n  border-radius: var(--radius-lg);\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--color-white);\n  box-shadow: var(--shadow-lg);\n}\n\n.trustBadge h3 {\n  margin-bottom: var(--spacing-sm);\n  font-size: 1.25rem;\n}\n\n.trustNumber {\n  font-size: 4rem;\n  font-weight: bold;\n  line-height: 1;\n  margin-bottom: var(--spacing-xs);\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.trustBadge p:last-child {\n  font-size: 1.125rem;\n  margin: 0;\n}\n\n/* Emergency Section */\n.emergency {\n  padding: var(--spacing-2xl) 0;\n  background: linear-gradient(135deg, var(--color-red), #b91c1c);\n  text-align: center;\n}\n\n.emergencyContent h2 {\n  color: var(--color-white);\n  font-size: 3rem;\n  margin-bottom: var(--spacing-md);\n}\n\n.emergencyContent p {\n  color: var(--color-white);\n  font-size: 1.25rem;\n  margin-bottom: var(--spacing-xl);\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.emergencyActions {\n  display: flex;\n  gap: var(--spacing-md);\n  justify-content: center;\n  align-items: center;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .heroContent {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-xl);\n    text-align: center;\n  }\n\n  .heroTitle {\n    font-size: 3rem;\n  }\n\n  .heroSubtitle {\n    font-size: 1.25rem;\n  }\n\n  .logoPlaceholder {\n    width: 250px;\n    height: 250px;\n  }\n\n  .logoPlaceholder h2 {\n    font-size: 2rem;\n  }\n\n  .whyContent {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-xl);\n  }\n\n  .emergencyActions {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .emergencyContent h2 {\n    font-size: 2.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .hero {\n    padding: var(--spacing-xl) 0;\n  }\n\n  .heroTitle {\n    font-size: 2.5rem;\n  }\n\n  .logoPlaceholder {\n    width: 200px;\n    height: 200px;\n  }\n\n  .logoPlaceholder h2 {\n    font-size: 1.5rem;\n  }\n\n  .serviceCard {\n    padding: var(--spacing-lg);\n  }\n\n  .trustNumber {\n    font-size: 3rem;\n  }\n\n  .emergencyContent h2 {\n    font-size: 2rem;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;AAKA;;;;;;;AAOA;;;;;;;;;AAYA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;AAQA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;AAQA;EACE;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}]}