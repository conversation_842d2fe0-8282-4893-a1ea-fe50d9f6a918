{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/* CSS Reset and Base Styles */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n:root {\n  /* Tuffside Brand Colors */\n  --color-black: #000000;\n  --color-white: #ffffff;\n  --color-red: #dc2626;\n  --color-grey: #6b7280;\n  --color-blue: #1e40af;\n\n  /* Typography */\n  --font-script: var(--font-mr-dafoe), '<PERSON><PERSON><PERSON>', cursive;\n  --font-sans: var(--font-inter), 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n\n  /* Spacing */\n  --spacing-xs: 0.5rem;\n  --spacing-sm: 1rem;\n  --spacing-md: 1.5rem;\n  --spacing-lg: 2rem;\n  --spacing-xl: 3rem;\n  --spacing-2xl: 4rem;\n\n  /* Border radius */\n  --radius-sm: 0.25rem;\n  --radius-md: 0.5rem;\n  --radius-lg: 1rem;\n\n  /* Shadows */\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);\n}\n\nbody {\n  background-color: var(--color-black);\n  color: var(--color-white);\n  font-family: var(--font-sans);\n  line-height: 1.6;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n/* Typography */\nh1, h2, h3, h4, h5, h6 {\n  font-family: var(--font-script);\n  font-weight: bold;\n  line-height: 1.2;\n  margin-bottom: var(--spacing-sm);\n}\n\nh1 {\n  font-size: 3rem;\n  margin-bottom: var(--spacing-md);\n}\n\nh2 {\n  font-size: 2.5rem;\n}\n\nh3 {\n  font-size: 2rem;\n}\n\nh4 {\n  font-size: 1.5rem;\n}\n\np {\n  margin-bottom: var(--spacing-sm);\n}\n\na {\n  color: var(--color-white);\n  text-decoration: none;\n  transition: color 0.3s ease;\n}\n\na:hover {\n  color: var(--color-red);\n}\n\n/* Utility Classes */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-sm);\n}\n\n.text-center {\n  text-align: center;\n}\n\n.text-red {\n  color: var(--color-red);\n}\n\n.text-grey {\n  color: var(--color-grey);\n}\n\n.mb-sm {\n  margin-bottom: var(--spacing-sm);\n}\n\n.mb-md {\n  margin-bottom: var(--spacing-md);\n}\n\n.mb-lg {\n  margin-bottom: var(--spacing-lg);\n}\n\n.mt-sm {\n  margin-top: var(--spacing-sm);\n}\n\n.mt-md {\n  margin-top: var(--spacing-md);\n}\n\n.mt-lg {\n  margin-top: var(--spacing-lg);\n}\n\n/* Button Styles */\n.btn {\n  display: inline-block;\n  padding: var(--spacing-sm) var(--spacing-md);\n  border: 2px solid var(--color-white);\n  border-radius: var(--radius-md);\n  background-color: transparent;\n  color: var(--color-white);\n  font-family: var(--font-sans);\n  font-weight: 600;\n  text-decoration: none;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn:hover {\n  background-color: var(--color-white);\n  color: var(--color-black);\n}\n\n.btn-primary {\n  background-color: var(--color-red);\n  border-color: var(--color-red);\n  color: var(--color-white);\n}\n\n.btn-primary:hover {\n  background-color: var(--color-white);\n  color: var(--color-red);\n  border-color: var(--color-white);\n}\n\n.btn-large {\n  padding: var(--spacing-md) var(--spacing-lg);\n  font-size: 1.125rem;\n}\n\n/* Grid and Layout */\n.grid {\n  display: grid;\n  gap: var(--spacing-md);\n}\n\n.grid-2 {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.grid-3 {\n  grid-template-columns: repeat(3, 1fr);\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-center {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.flex-between {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.flex-column {\n  flex-direction: column;\n}\n\n.gap-sm {\n  gap: var(--spacing-sm);\n}\n\n.gap-md {\n  gap: var(--spacing-md);\n}\n\n.gap-lg {\n  gap: var(--spacing-lg);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 var(--spacing-sm);\n  }\n\n  h1 {\n    font-size: 2.5rem;\n  }\n\n  h2 {\n    font-size: 2rem;\n  }\n\n  h3 {\n    font-size: 1.5rem;\n  }\n\n  .grid-2,\n  .grid-3 {\n    grid-template-columns: 1fr;\n  }\n\n  .btn-large {\n    padding: var(--spacing-sm) var(--spacing-md);\n    font-size: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;;;AA+BA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAKA"}}]}