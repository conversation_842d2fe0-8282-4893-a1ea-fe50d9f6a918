/* [project]/src/components/CTAButtons.module.css [app-client] (css) */
.CTAButtons-module__AIDVgW__ctaContainer {
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
  display: flex;
}

.CTAButtons-module__AIDVgW__horizontal {
  flex-flow: wrap;
}

.CTAButtons-module__AIDVgW__vertical {
  flex-direction: column;
  align-items: stretch;
}

.CTAButtons-module__AIDVgW__small .CTAButtons-module__AIDVgW__btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: .875rem;
}

@media (width <= 768px) {
  .CTAButtons-module__AIDVgW__horizontal {
    flex-direction: column;
    align-items: stretch;
  }

  .CTAButtons-module__AIDVgW__ctaContainer {
    gap: var(--spacing-sm);
  }
}

@media (width <= 480px) {
  .CTAButtons-module__AIDVgW__ctaContainer .CTAButtons-module__AIDVgW__btn {
    text-align: center;
    width: 100%;
  }
}


/* [project]/src/app/page.module.css [app-client] (css) */
.page-module___8aEwW__homePage {
  min-height: 100vh;
}

.page-module___8aEwW__hero {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
  position: relative;
  overflow: hidden;
}

.page-module___8aEwW__hero:before {
  content: "";
  opacity: .05;
  z-index: 1;
  background: url("/images/pattern_bg.webp") center / cover no-repeat;
  position: absolute;
  inset: 0;
}

.page-module___8aEwW__heroContent {
  gap: var(--spacing-2xl);
  z-index: 2;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  display: grid;
  position: relative;
}

.page-module___8aEwW__heroText {
  max-width: 600px;
}

.page-module___8aEwW__heroTitle {
  font-family: var(--font-script);
  margin-bottom: var(--spacing-sm);
  color: var(--color-white);
  text-shadow: 2px 2px 4px #00000080;
  font-size: 4rem;
  line-height: 1;
}

.page-module___8aEwW__heroSubtitle {
  color: var(--color-red);
  margin-top: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: 1.5rem;
  display: block;
}

.page-module___8aEwW__heroTagline {
  color: var(--color-red);
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
  font-style: italic;
  font-weight: 600;
}

.page-module___8aEwW__heroDescription {
  color: var(--color-grey);
  margin-bottom: var(--spacing-xl);
  font-size: 1.125rem;
  line-height: 1.6;
}

.page-module___8aEwW__heroImage {
  justify-content: center;
  align-items: center;
  display: flex;
}

.page-module___8aEwW__logoPlaceholder {
  border: 3px solid var(--color-red);
  background: linear-gradient(45deg, var(--color-black), #2a2a2a);
  border-radius: 50%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 300px;
  height: 300px;
  display: flex;
  box-shadow: 0 0 30px #dc26264d;
}

.page-module___8aEwW__logoPlaceholder h2 {
  font-family: var(--font-script);
  color: var(--color-white);
  text-shadow: 2px 2px 4px #00000080;
  margin: 0;
  font-size: 2.5rem;
}

.page-module___8aEwW__logoPlaceholder p {
  color: var(--color-red);
  text-transform: uppercase;
  letter-spacing: .2em;
  margin: 0;
  font-size: 1rem;
}

.page-module___8aEwW__servicesPreview {
  padding: var(--spacing-2xl) 0;
  background-color: #111;
}

.page-module___8aEwW__serviceCard {
  background-color: var(--color-black);
  border: 2px solid var(--color-grey);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all .3s;
  position: relative;
  overflow: hidden;
}

.page-module___8aEwW__serviceCard:before {
  content: "";
  background: linear-gradient(90deg, #0000, #dc26261a, #0000);
  width: 100%;
  height: 100%;
  transition: left .5s;
  position: absolute;
  top: 0;
  left: -100%;
}

.page-module___8aEwW__serviceCard:hover:before {
  left: 100%;
}

.page-module___8aEwW__serviceCard:hover {
  border-color: var(--color-red);
  box-shadow: var(--shadow-lg);
  transform: translateY(-5px);
}

.page-module___8aEwW__serviceIcon {
  margin-bottom: var(--spacing-md);
  font-size: 3rem;
}

.page-module___8aEwW__serviceCard h3 {
  color: var(--color-white);
  margin-bottom: var(--spacing-sm);
}

.page-module___8aEwW__serviceCard p {
  color: var(--color-grey);
  line-height: 1.6;
}

.page-module___8aEwW__whyChooseUs {
  padding: var(--spacing-2xl) 0;
  background-color: var(--color-black);
}

.page-module___8aEwW__whyContent {
  gap: var(--spacing-2xl);
  grid-template-columns: 2fr 1fr;
  align-items: center;
  display: grid;
}

.page-module___8aEwW__whyText h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.page-module___8aEwW__whyList {
  margin-bottom: var(--spacing-lg);
  padding: 0;
  list-style: none;
}

.page-module___8aEwW__whyList li {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  font-size: 1.125rem;
  transition: color .3s;
}

.page-module___8aEwW__whyList li:hover {
  color: var(--color-white);
}

.page-module___8aEwW__whyText p {
  color: var(--color-grey);
  font-size: 1.125rem;
  line-height: 1.6;
}

.page-module___8aEwW__trustBadge {
  background: linear-gradient(135deg, var(--color-red), #b91c1c);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--color-white);
  box-shadow: var(--shadow-lg);
}

.page-module___8aEwW__trustBadge h3 {
  margin-bottom: var(--spacing-sm);
  font-size: 1.25rem;
}

.page-module___8aEwW__trustNumber {
  margin-bottom: var(--spacing-xs);
  text-shadow: 2px 2px 4px #0000004d;
  font-size: 4rem;
  font-weight: bold;
  line-height: 1;
}

.page-module___8aEwW__trustBadge p:last-child {
  margin: 0;
  font-size: 1.125rem;
}

.page-module___8aEwW__emergency {
  padding: var(--spacing-2xl) 0;
  background: linear-gradient(135deg, var(--color-red), #b91c1c);
  text-align: center;
}

.page-module___8aEwW__emergencyContent h2 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: 3rem;
}

.page-module___8aEwW__emergencyContent p {
  color: var(--color-white);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.25rem;
}

.page-module___8aEwW__emergencyActions {
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
  display: flex;
}

@media (width <= 768px) {
  .page-module___8aEwW__heroContent {
    gap: var(--spacing-xl);
    text-align: center;
    grid-template-columns: 1fr;
  }

  .page-module___8aEwW__heroTitle {
    font-size: 3rem;
  }

  .page-module___8aEwW__heroSubtitle {
    font-size: 1.25rem;
  }

  .page-module___8aEwW__logoPlaceholder {
    width: 250px;
    height: 250px;
  }

  .page-module___8aEwW__logoPlaceholder h2 {
    font-size: 2rem;
  }

  .page-module___8aEwW__whyContent {
    gap: var(--spacing-xl);
    grid-template-columns: 1fr;
  }

  .page-module___8aEwW__emergencyActions {
    flex-direction: column;
    align-items: stretch;
  }

  .page-module___8aEwW__emergencyContent h2 {
    font-size: 2.5rem;
  }
}

@media (width <= 480px) {
  .page-module___8aEwW__hero {
    padding: var(--spacing-xl) 0;
  }

  .page-module___8aEwW__heroTitle {
    font-size: 2.5rem;
  }

  .page-module___8aEwW__logoPlaceholder {
    width: 200px;
    height: 200px;
  }

  .page-module___8aEwW__logoPlaceholder h2 {
    font-size: 1.5rem;
  }

  .page-module___8aEwW__serviceCard {
    padding: var(--spacing-lg);
  }

  .page-module___8aEwW__trustNumber {
    font-size: 3rem;
  }

  .page-module___8aEwW__emergencyContent h2 {
    font-size: 2rem;
  }
}


/*# sourceMappingURL=src_d252336f._.css.map*/