/* [next]/internal/font/google/mr_dafoe_e0d797e1.module.css [app-client] (css) */
@font-face {
  font-family: Mr <PERSON><PERSON><PERSON>;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/lJwE_pIzkS5NXuMMrFijibIgxC_DZdM-s.d4957f2a.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Mr <PERSON><PERSON><PERSON>;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/lJwE_pIzkS5NXuMMrFitibIgxC_D-s.p.208619da.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Mr Dafoe Fallback;
  src: local(Arial);
  ascent-override: 121.14%;
  descent-override: 51.37%;
  line-gap-override: 0.0%;
  size-adjust: 80.98%;
}

.mr_dafoe_e0d797e1-module__2Wu4FG__className {
  font-family: Mr Dafoe, Mr Dafoe Fallback;
  font-style: normal;
  font-weight: 400;
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_mr_dafoe_e0d797e1_module_css_f9ee138c._.single.css.map*/