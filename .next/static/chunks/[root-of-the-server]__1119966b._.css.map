{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_92243eee.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/mr_dafoe_dea4b7cd.module.css"], "sourcesContent": ["/* latin-ext */\n@font-face {\n  font-family: 'Mr <PERSON><PERSON><PERSON>';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/mrdafoe/v14/lJwE-pIzkS5NXuMMrFijibIgxC_DZdM.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Mr <PERSON><PERSON><PERSON>';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/mrdafoe/v14/lJwE-pIzkS5NXuMMrFitibIgxC_D.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Mr Dafoe Fallback';\n    src: local(\"Arial\");\n    ascent-override: 121.14%;\ndescent-override: 51.37%;\nline-gap-override: 0.00%;\nsize-adjust: 80.98%;\n\n}\n.className {\n    font-family: 'Mr Dafoe', 'Mr Dafoe Fallback';\n    font-weight: 400;\nfont-style: normal;\n\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/app/globals.css"], "sourcesContent": ["/* CSS Reset and Base Styles */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n:root {\n  /* Tuffside Brand Colors */\n  --color-black: #000000;\n  --color-white: #ffffff;\n  --color-red: #dc2626;\n  --color-grey: #6b7280;\n  --color-blue: #1e40af;\n\n  /* Typography */\n  --font-script: var(--font-mr-dafoe), '<PERSON><PERSON><PERSON>', cursive;\n  --font-sans: var(--font-inter), 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n\n  /* Spacing */\n  --spacing-xs: 0.5rem;\n  --spacing-sm: 1rem;\n  --spacing-md: 1.5rem;\n  --spacing-lg: 2rem;\n  --spacing-xl: 3rem;\n  --spacing-2xl: 4rem;\n\n  /* Border radius */\n  --radius-sm: 0.25rem;\n  --radius-md: 0.5rem;\n  --radius-lg: 1rem;\n\n  /* Shadows */\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);\n}\n\nbody {\n  background-color: var(--color-black);\n  color: var(--color-white);\n  font-family: var(--font-sans);\n  line-height: 1.6;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n/* Typography */\nh1, h2, h3, h4, h5, h6 {\n  font-family: var(--font-script);\n  font-weight: bold;\n  line-height: 1.2;\n  margin-bottom: var(--spacing-sm);\n}\n\nh1 {\n  font-size: 3rem;\n  margin-bottom: var(--spacing-md);\n}\n\nh2 {\n  font-size: 2.5rem;\n}\n\nh3 {\n  font-size: 2rem;\n}\n\nh4 {\n  font-size: 1.5rem;\n}\n\np {\n  margin-bottom: var(--spacing-sm);\n}\n\na {\n  color: var(--color-white);\n  text-decoration: none;\n  transition: color 0.3s ease;\n}\n\na:hover {\n  color: var(--color-red);\n}\n\n/* Utility Classes */\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 var(--spacing-sm);\n}\n\n.text-center {\n  text-align: center;\n}\n\n.text-red {\n  color: var(--color-red);\n}\n\n.text-grey {\n  color: var(--color-grey);\n}\n\n.mb-sm {\n  margin-bottom: var(--spacing-sm);\n}\n\n.mb-md {\n  margin-bottom: var(--spacing-md);\n}\n\n.mb-lg {\n  margin-bottom: var(--spacing-lg);\n}\n\n.mt-sm {\n  margin-top: var(--spacing-sm);\n}\n\n.mt-md {\n  margin-top: var(--spacing-md);\n}\n\n.mt-lg {\n  margin-top: var(--spacing-lg);\n}\n\n/* Button Styles */\n.btn {\n  display: inline-block;\n  padding: var(--spacing-sm) var(--spacing-md);\n  border: 2px solid var(--color-white);\n  border-radius: var(--radius-md);\n  background-color: transparent;\n  color: var(--color-white);\n  font-family: var(--font-sans);\n  font-weight: 600;\n  text-decoration: none;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.btn:hover {\n  background-color: var(--color-white);\n  color: var(--color-black);\n}\n\n.btn-primary {\n  background-color: var(--color-red);\n  border-color: var(--color-red);\n  color: var(--color-white);\n}\n\n.btn-primary:hover {\n  background-color: var(--color-white);\n  color: var(--color-red);\n  border-color: var(--color-white);\n}\n\n.btn-large {\n  padding: var(--spacing-md) var(--spacing-lg);\n  font-size: 1.125rem;\n}\n\n/* Grid and Layout */\n.grid {\n  display: grid;\n  gap: var(--spacing-md);\n}\n\n.grid-2 {\n  grid-template-columns: repeat(2, 1fr);\n}\n\n.grid-3 {\n  grid-template-columns: repeat(3, 1fr);\n}\n\n.flex {\n  display: flex;\n}\n\n.flex-center {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.flex-between {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.flex-column {\n  flex-direction: column;\n}\n\n.gap-sm {\n  gap: var(--spacing-sm);\n}\n\n.gap-md {\n  gap: var(--spacing-md);\n}\n\n.gap-lg {\n  gap: var(--spacing-lg);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .container {\n    padding: 0 var(--spacing-sm);\n  }\n\n  h1 {\n    font-size: 2.5rem;\n  }\n\n  h2 {\n    font-size: 2rem;\n  }\n\n  h3 {\n    font-size: 1.5rem;\n  }\n\n  .grid-2,\n  .grid-3 {\n    grid-template-columns: 1fr;\n  }\n\n  .btn-large {\n    padding: var(--spacing-sm) var(--spacing-md);\n    font-size: 1rem;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;;;AA+BA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAKA", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/Header.module.css"], "sourcesContent": [".header {\n  background-color: var(--color-black);\n  border-bottom: 2px solid var(--color-red);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  padding: var(--spacing-sm) 0;\n}\n\n.headerContent {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n}\n\n.logo {\n  text-decoration: none;\n  color: var(--color-white);\n}\n\n.logo h1 {\n  font-family: var(--font-script);\n  font-size: 2.5rem;\n  margin: 0;\n  color: var(--color-white);\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n}\n\n.tagline {\n  font-family: var(--font-sans);\n  font-size: 0.875rem;\n  color: var(--color-grey);\n  display: block;\n  margin-top: -0.5rem;\n  text-transform: uppercase;\n  letter-spacing: 0.1em;\n}\n\n.nav {\n  display: flex;\n  gap: var(--spacing-md);\n  align-items: center;\n}\n\n.navLink {\n  color: var(--color-white);\n  text-decoration: none;\n  font-weight: 500;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.navLink:hover {\n  color: var(--color-red);\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.navLink::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 50%;\n  width: 0;\n  height: 2px;\n  background-color: var(--color-red);\n  transition: all 0.3s ease;\n  transform: translateX(-50%);\n}\n\n.navLink:hover::after {\n  width: 100%;\n}\n\n.ctaButtons {\n  display: flex;\n  gap: var(--spacing-sm);\n  align-items: center;\n}\n\n.mobileMenuButton {\n  display: none;\n  flex-direction: column;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  gap: 4px;\n}\n\n.hamburger {\n  width: 25px;\n  height: 3px;\n  background-color: var(--color-white);\n  transition: all 0.3s ease;\n  border-radius: 2px;\n}\n\n.mobileNav {\n  display: none;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-md) 0;\n  border-top: 1px solid var(--color-grey);\n  margin-top: var(--spacing-sm);\n}\n\n.mobileNavOpen {\n  display: flex;\n}\n\n.mobileNavLink {\n  color: var(--color-white);\n  text-decoration: none;\n  padding: var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  transition: all 0.3s ease;\n  text-align: center;\n  font-weight: 500;\n}\n\n.mobileNavLink:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  color: var(--color-red);\n}\n\n.mobileCta {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n  margin-top: var(--spacing-md);\n  padding-top: var(--spacing-md);\n  border-top: 1px solid var(--color-grey);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .nav,\n  .ctaButtons {\n    display: none;\n  }\n\n  .mobileMenuButton {\n    display: flex;\n  }\n\n  .logo h1 {\n    font-size: 2rem;\n  }\n\n  .tagline {\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .logo h1 {\n    font-size: 1.75rem;\n  }\n  \n  .headerContent {\n    gap: var(--spacing-sm);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;AAUA;EACE;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Workspace/APP/Tuffside/src/components/Footer.module.css"], "sourcesContent": [".footer {\n  background-color: var(--color-black);\n  border-top: 2px solid var(--color-red);\n  margin-top: var(--spacing-2xl);\n  padding: var(--spacing-2xl) 0 var(--spacing-lg);\n}\n\n.footerContent {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--spacing-xl);\n  margin-bottom: var(--spacing-xl);\n}\n\n.footerSection h3 {\n  font-family: var(--font-script);\n  color: var(--color-white);\n  font-size: 2rem;\n  margin-bottom: var(--spacing-sm);\n}\n\n.footerSection h4 {\n  font-family: var(--font-script);\n  color: var(--color-red);\n  font-size: 1.5rem;\n  margin-bottom: var(--spacing-sm);\n}\n\n.tagline {\n  color: var(--color-red);\n  font-style: italic;\n  font-weight: 600;\n  margin-bottom: var(--spacing-sm);\n}\n\n.footerSection p {\n  color: var(--color-grey);\n  line-height: 1.6;\n  margin-bottom: var(--spacing-sm);\n}\n\n.footerNav {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n\n.footerLink {\n  color: var(--color-grey);\n  text-decoration: none;\n  padding: var(--spacing-xs) 0;\n  transition: color 0.3s ease;\n  border-bottom: 1px solid transparent;\n}\n\n.footerLink:hover {\n  color: var(--color-white);\n  border-bottom-color: var(--color-red);\n}\n\n.contactInfo {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n\n.contactInfo p {\n  margin-bottom: 0;\n}\n\n.contactLink {\n  color: var(--color-white);\n  text-decoration: none;\n  transition: color 0.3s ease;\n}\n\n.contactLink:hover {\n  color: var(--color-red);\n}\n\n.servicesList {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.servicesList li {\n  color: var(--color-grey);\n  padding: var(--spacing-xs) 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  transition: color 0.3s ease;\n}\n\n.servicesList li:hover {\n  color: var(--color-white);\n}\n\n.servicesList li:last-child {\n  border-bottom: none;\n}\n\n.footerBottom {\n  border-top: 1px solid var(--color-grey);\n  padding-top: var(--spacing-lg);\n}\n\n.footerBottomContent {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n\n.footerBottomContent p {\n  color: var(--color-grey);\n  margin: 0;\n}\n\n.footerCta {\n  display: flex;\n  gap: var(--spacing-sm);\n  align-items: center;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .footerContent {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-lg);\n  }\n\n  .footerBottomContent {\n    flex-direction: column;\n    text-align: center;\n    gap: var(--spacing-md);\n  }\n\n  .footerCta {\n    justify-content: center;\n  }\n}\n\n@media (max-width: 480px) {\n  .footer {\n    padding: var(--spacing-xl) 0 var(--spacing-md);\n  }\n\n  .footerContent {\n    gap: var(--spacing-md);\n  }\n\n  .footerSection h3 {\n    font-size: 1.75rem;\n  }\n\n  .footerSection h4 {\n    font-size: 1.25rem;\n  }\n\n  .footerCta {\n    flex-direction: column;\n    width: 100%;\n  }\n\n  .footerCta .btn {\n    width: 100%;\n    text-align: center;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;AAOA;EACE;;;;;EAKA;;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA", "debugId": null}}]}