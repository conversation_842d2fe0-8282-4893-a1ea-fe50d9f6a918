/* [next]/internal/font/google/inter_92243eee.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n_wU-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n_wU-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n_wU-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n_wU-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n_wU-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n_wU-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_92243eee-module__hBCtSW__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}


/* [next]/internal/font/google/mr_dafoe_dea4b7cd.module.css [app-client] (css) */
@font-face {
  font-family: Mr Dafoe;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/lJwE_pIzkS5NXuMMrFijibIgxC_DZdM-s.d4957f2a.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Mr Dafoe;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/lJwE_pIzkS5NXuMMrFitibIgxC_D-s.p.208619da.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Mr Dafoe Fallback;
  src: local(Arial);
  ascent-override: 121.14%;
  descent-override: 51.37%;
  line-gap-override: 0.0%;
  size-adjust: 80.98%;
}

.mr_dafoe_dea4b7cd-module__B2fKgW__className {
  font-family: Mr Dafoe, Mr Dafoe Fallback;
  font-style: normal;
  font-weight: 400;
}


/* [project]/src/app/globals.css [app-client] (css) */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --color-black: #000;
  --color-white: #fff;
  --color-red: #dc2626;
  --color-grey: #6b7280;
  --color-blue: #1e40af;
  --font-script: var(--font-mr-dafoe), "Mr Dafoe", cursive;
  --font-sans: var(--font-inter), "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --spacing-xs: .5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;
  --radius-sm: .25rem;
  --radius-md: .5rem;
  --radius-lg: 1rem;
  --shadow-sm: 0 1px 2px 0 #0000000d;
  --shadow-md: 0 4px 6px -1px #0000001a;
  --shadow-lg: 0 10px 15px -3px #0000001a;
}

body {
  background-color: var(--color-black);
  color: var(--color-white);
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-script);
  margin-bottom: var(--spacing-sm);
  font-weight: bold;
  line-height: 1.2;
}

h1 {
  margin-bottom: var(--spacing-md);
  font-size: 3rem;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-sm);
}

a {
  color: var(--color-white);
  text-decoration: none;
  transition: color .3s;
}

a:hover {
  color: var(--color-red);
}

.container {
  max-width: 1200px;
  padding: 0 var(--spacing-sm);
  margin: 0 auto;
}

.text-center {
  text-align: center;
}

.text-red {
  color: var(--color-red);
}

.text-grey {
  color: var(--color-grey);
}

.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-lg {
  margin-bottom: var(--spacing-lg);
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--color-white);
  border-radius: var(--radius-md);
  color: var(--color-white);
  font-family: var(--font-sans);
  text-align: center;
  cursor: pointer;
  background-color: #0000;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: inline-block;
}

.btn:hover {
  background-color: var(--color-white);
  color: var(--color-black);
}

.btn-primary {
  background-color: var(--color-red);
  border-color: var(--color-red);
  color: var(--color-white);
}

.btn-primary:hover {
  background-color: var(--color-white);
  color: var(--color-red);
  border-color: var(--color-white);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.125rem;
}

.grid {
  gap: var(--spacing-md);
  display: grid;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.flex {
  display: flex;
}

.flex-center {
  justify-content: center;
  align-items: center;
  display: flex;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

@media (width <= 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  .grid-2, .grid-3 {
    grid-template-columns: 1fr;
  }

  .btn-large {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 1rem;
  }
}


/* [project]/src/components/Header.module.css [app-client] (css) */
.Header-module__hBw1pG__header {
  background-color: var(--color-black);
  border-bottom: 2px solid var(--color-red);
  z-index: 1000;
  padding: var(--spacing-sm) 0;
  position: sticky;
  top: 0;
}

.Header-module__hBw1pG__headerContent {
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
  display: flex;
}

.Header-module__hBw1pG__logo {
  color: var(--color-white);
  text-decoration: none;
}

.Header-module__hBw1pG__logo h1 {
  font-family: var(--font-script);
  color: var(--color-white);
  text-shadow: 2px 2px 4px #00000080;
  margin: 0;
  font-size: 2.5rem;
}

.Header-module__hBw1pG__tagline {
  font-family: var(--font-sans);
  color: var(--color-grey);
  text-transform: uppercase;
  letter-spacing: .1em;
  margin-top: -.5rem;
  font-size: .875rem;
  display: block;
}

.Header-module__hBw1pG__nav {
  gap: var(--spacing-md);
  align-items: center;
  display: flex;
}

.Header-module__hBw1pG__navLink {
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  position: relative;
}

.Header-module__hBw1pG__navLink:hover {
  color: var(--color-red);
  background-color: #ffffff1a;
}

.Header-module__hBw1pG__navLink:after {
  content: "";
  background-color: var(--color-red);
  width: 0;
  height: 2px;
  transition: all .3s;
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
}

.Header-module__hBw1pG__navLink:hover:after {
  width: 100%;
}

.Header-module__hBw1pG__ctaButtons {
  gap: var(--spacing-sm);
  align-items: center;
  display: flex;
}

.Header-module__hBw1pG__mobileMenuButton {
  cursor: pointer;
  padding: var(--spacing-xs);
  background: none;
  border: none;
  flex-direction: column;
  gap: 4px;
  display: none;
}

.Header-module__hBw1pG__hamburger {
  background-color: var(--color-white);
  border-radius: 2px;
  width: 25px;
  height: 3px;
  transition: all .3s;
}

.Header-module__hBw1pG__mobileNav {
  gap: var(--spacing-sm);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--color-grey);
  margin-top: var(--spacing-sm);
  flex-direction: column;
  display: none;
}

.Header-module__hBw1pG__mobileNavOpen {
  display: flex;
}

.Header-module__hBw1pG__mobileNavLink {
  color: var(--color-white);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  text-align: center;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
}

.Header-module__hBw1pG__mobileNavLink:hover {
  color: var(--color-red);
  background-color: #ffffff1a;
}

.Header-module__hBw1pG__mobileCta {
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-grey);
  flex-direction: column;
  display: flex;
}

@media (width <= 768px) {
  .Header-module__hBw1pG__nav, .Header-module__hBw1pG__ctaButtons {
    display: none;
  }

  .Header-module__hBw1pG__mobileMenuButton {
    display: flex;
  }

  .Header-module__hBw1pG__logo h1 {
    font-size: 2rem;
  }

  .Header-module__hBw1pG__tagline {
    font-size: .75rem;
  }
}

@media (width <= 480px) {
  .Header-module__hBw1pG__logo h1 {
    font-size: 1.75rem;
  }

  .Header-module__hBw1pG__headerContent {
    gap: var(--spacing-sm);
  }
}


/* [project]/src/components/Footer.module.css [app-client] (css) */
.Footer-module__S6Hkya__footer {
  background-color: var(--color-black);
  border-top: 2px solid var(--color-red);
  margin-top: var(--spacing-2xl);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.Footer-module__S6Hkya__footerContent {
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  display: grid;
}

.Footer-module__S6Hkya__footerSection h3 {
  font-family: var(--font-script);
  color: var(--color-white);
  margin-bottom: var(--spacing-sm);
  font-size: 2rem;
}

.Footer-module__S6Hkya__footerSection h4 {
  font-family: var(--font-script);
  color: var(--color-red);
  margin-bottom: var(--spacing-sm);
  font-size: 1.5rem;
}

.Footer-module__S6Hkya__tagline {
  color: var(--color-red);
  margin-bottom: var(--spacing-sm);
  font-style: italic;
  font-weight: 600;
}

.Footer-module__S6Hkya__footerSection p {
  color: var(--color-grey);
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

.Footer-module__S6Hkya__footerNav {
  gap: var(--spacing-xs);
  flex-direction: column;
  display: flex;
}

.Footer-module__S6Hkya__footerLink {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid #0000;
  text-decoration: none;
  transition: color .3s;
}

.Footer-module__S6Hkya__footerLink:hover {
  color: var(--color-white);
  border-bottom-color: var(--color-red);
}

.Footer-module__S6Hkya__contactInfo {
  gap: var(--spacing-xs);
  flex-direction: column;
  display: flex;
}

.Footer-module__S6Hkya__contactInfo p {
  margin-bottom: 0;
}

.Footer-module__S6Hkya__contactLink {
  color: var(--color-white);
  text-decoration: none;
  transition: color .3s;
}

.Footer-module__S6Hkya__contactLink:hover {
  color: var(--color-red);
}

.Footer-module__S6Hkya__servicesList {
  margin: 0;
  padding: 0;
  list-style: none;
}

.Footer-module__S6Hkya__servicesList li {
  color: var(--color-grey);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid #ffffff1a;
  transition: color .3s;
}

.Footer-module__S6Hkya__servicesList li:hover {
  color: var(--color-white);
}

.Footer-module__S6Hkya__servicesList li:last-child {
  border-bottom: none;
}

.Footer-module__S6Hkya__footerBottom {
  border-top: 1px solid var(--color-grey);
  padding-top: var(--spacing-lg);
}

.Footer-module__S6Hkya__footerBottomContent {
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
  display: flex;
}

.Footer-module__S6Hkya__footerBottomContent p {
  color: var(--color-grey);
  margin: 0;
}

.Footer-module__S6Hkya__footerCta {
  gap: var(--spacing-sm);
  align-items: center;
  display: flex;
}

@media (width <= 768px) {
  .Footer-module__S6Hkya__footerContent {
    gap: var(--spacing-lg);
    grid-template-columns: 1fr;
  }

  .Footer-module__S6Hkya__footerBottomContent {
    text-align: center;
    gap: var(--spacing-md);
    flex-direction: column;
  }

  .Footer-module__S6Hkya__footerCta {
    justify-content: center;
  }
}

@media (width <= 480px) {
  .Footer-module__S6Hkya__footer {
    padding: var(--spacing-xl) 0 var(--spacing-md);
  }

  .Footer-module__S6Hkya__footerContent {
    gap: var(--spacing-md);
  }

  .Footer-module__S6Hkya__footerSection h3 {
    font-size: 1.75rem;
  }

  .Footer-module__S6Hkya__footerSection h4 {
    font-size: 1.25rem;
  }

  .Footer-module__S6Hkya__footerCta {
    flex-direction: column;
    width: 100%;
  }

  .Footer-module__S6Hkya__footerCta .Footer-module__S6Hkya__btn {
    text-align: center;
    width: 100%;
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__1119966b._.css.map*/