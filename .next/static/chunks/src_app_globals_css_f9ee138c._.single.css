/* [project]/src/app/globals.css [app-client] (css) */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --color-black: #000;
  --color-white: #fff;
  --color-red: #dc2626;
  --color-grey: #6b7280;
  --color-blue: #1e40af;
  --font-script: "<PERSON> <PERSON><PERSON><PERSON>", cursive;
  --font-sans: var(--font-inter), "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --spacing-xs: .5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;
  --radius-sm: .25rem;
  --radius-md: .5rem;
  --radius-lg: 1rem;
  --shadow-sm: 0 1px 2px 0 #0000000d;
  --shadow-md: 0 4px 6px -1px #0000001a;
  --shadow-lg: 0 10px 15px -3px #0000001a;
}

body {
  background-color: var(--color-black);
  color: var(--color-white);
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-script);
  margin-bottom: var(--spacing-sm);
  font-weight: bold;
  line-height: 1.2;
}

h1 {
  margin-bottom: var(--spacing-md);
  font-size: 3rem;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-sm);
}

a {
  color: var(--color-white);
  text-decoration: none;
  transition: color .3s;
}

a:hover {
  color: var(--color-red);
}

.container {
  max-width: 1200px;
  padding: 0 var(--spacing-sm);
  margin: 0 auto;
}

.text-center {
  text-align: center;
}

.text-red {
  color: var(--color-red);
}

.text-grey {
  color: var(--color-grey);
}

.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-lg {
  margin-bottom: var(--spacing-lg);
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--color-white);
  border-radius: var(--radius-md);
  color: var(--color-white);
  font-family: var(--font-sans);
  text-align: center;
  cursor: pointer;
  background-color: #0000;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: inline-block;
}

.btn:hover {
  background-color: var(--color-white);
  color: var(--color-black);
}

.btn-primary {
  background-color: var(--color-red);
  border-color: var(--color-red);
  color: var(--color-white);
}

.btn-primary:hover {
  background-color: var(--color-white);
  color: var(--color-red);
  border-color: var(--color-white);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.125rem;
}

.grid {
  gap: var(--spacing-md);
  display: grid;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.flex {
  display: flex;
}

.flex-center {
  justify-content: center;
  align-items: center;
  display: flex;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

@media (width <= 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  .grid-2, .grid-3 {
    grid-template-columns: 1fr;
  }

  .btn-large {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 1rem;
  }
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/